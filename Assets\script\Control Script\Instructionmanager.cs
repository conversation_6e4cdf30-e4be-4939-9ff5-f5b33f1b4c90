using UnityEngine;
using UnityEngine.UI;

public class Instructionmanager : MonoBehaviour
{
    public string[] instructionText;
    public Text instruction;
    public int currentLevel = 0;

    void Start() => UpdateText();

    public void NextLevel()
    {
        currentLevel++;
        UpdateText();
    }

    public void SetLevel(int level)
    {
        currentLevel = level;
        UpdateText();
    }

    void UpdateText()
    {
        if (instructionText != null && instructionText.Length > 0 && instruction != null)
        {
            currentLevel = Mathf.Clamp(currentLevel, 0, instructionText.Length - 1);
            instruction.text = instructionText[currentLevel];
        }
    }
}
