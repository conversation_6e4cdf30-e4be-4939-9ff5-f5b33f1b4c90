using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class Instructionmanager : MonoBeh<PERSON>our
{
    [Header("Instruction levelwise")]
    public string[] instructionText;
    public Text instruction;
    public int currentLevel = 0;

    void Start()
    {
        UpdateInstructionText();
    }

    public void UpdateInstructionText()
    {
        if (instructionText != null && instructionText.Length > 0 && instruction != null)
        {
            // Ensure currentLevel is within bounds
            if (currentLevel >= 0 && currentLevel < instructionText.Length)
            {
                instruction.text = instructionText[currentLevel];
            }
            else
            {
                Debug.LogWarning("Current level is out of bounds for instruction text array");
            }
        }
        else
        {
            Debug.LogWarning("Instruction text array or instruction UI component is not properly set");
        }
    }

    public void SetLevel(int newLevel)
    {
        currentLevel = newLevel;
        UpdateInstructionText();
    }

    public void NextLevel()
    {
        if (currentLevel < instructionText.Length - 1)
        {
            currentLevel++;
            UpdateInstructionText();
        }
    }

    public void PreviousLevel()
    {
        if (currentLevel > 0)
        {
            currentLevel--;
            UpdateInstructionText();
        }
    }
}
